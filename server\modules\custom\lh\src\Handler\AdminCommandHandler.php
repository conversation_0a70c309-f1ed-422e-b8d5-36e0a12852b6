<?php

declare(strict_types=1);

namespace Drupal\lh\Handler;

use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Service\GameManager;
use Drupal\lh\Service\WechatApiClient;

/**
 * Handles admin management commands.
 */
class AdminCommandHandler extends AbstractMessageHandler {

  public function __construct(
    private readonly GameManager $gameManager,
    WechatApiClient $wechatApiClient,
    LoggerChannelInterface $logger,
  ) {
    parent::__construct($wechatApiClient, $logger);
  }

  /**
   * {@inheritdoc}
   */
  public function canHandle(array $data): bool {
    $messageData = $data['data'] ?? [];
    
    if (!$this->isMessageFromSelf($messageData) || !$this->isGroupMessage($messageData)) {
      return FALSE;
    }

    $message = $messageData['msg'] ?? '';
    return $this->isAdminCommand($message, 'admin_set') || $this->isAdminCommand($message, 'admin_remove');
  }

  /**
   * {@inheritdoc}
   */
  public function handle(array $data): array {
    $messageData = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    $message = $messageData['msg'] ?? '';
    $isSetAdmin = $this->isAdminCommand($message, 'admin_set');
    $isRemoveAdmin = $this->isAdminCommand($message, 'admin_remove');

    if (!$isSetAdmin && !$isRemoveAdmin) {
      return ['status' => 'ignored', 'reason' => 'Not an admin management command'];
    }

    $atWxidList = $messageData['atWxidList'] ?? [];
    if (empty($atWxidList)) {
      $wxidRoom = $messageData['fromWxid'] ?? '';
      if (!empty($wxidRoom)) {
        $example = $isSetAdmin ? '@张三 设置管理员' : '@张三 移除管理员';
        $this->sendGroupMessage($wxidRoom, "请@需要的人员\r示例：{$example}", $port);
      }
      return ['status' => 'ignored', 'reason' => 'No mentioned users'];
    }

    $wxidRoom = $messageData['fromWxid'] ?? '';
    $info = $this->getCurrentWechatInfo();
    $currentWxid = $info['result']['wxid'] ?? NULL;

    if (empty($wxidRoom) || !$currentWxid) {
      return ['status' => 'error', 'reason' => 'Missing required data'];
    }

    $roomResult = $isSetAdmin
      ? $this->gameManager->createOrUpdateRoom($wxidRoom, $currentWxid, $atWxidList)
      : $this->gameManager->removeManagersFromRoom($wxidRoom, $atWxidList);

    $managerCount = count($roomResult['managers'] ?? []);
    $action = $isSetAdmin ? 'set' : 'remove';

    if ($isSetAdmin) {
      $actionText = $roomResult['action'] === 'created' ? '新建' : '更新';
      $message = $roomResult['success']
        ? "🎉 管理员设置完成！\r✅ 房间{$actionText}成功\r房间ID: {$roomResult['room_id']}\r管理员数量: {$managerCount}"
        : "🎉 管理员设置完成！\r❌ 房间操作失败: {$roomResult['message']}";
    } else {
      $message = $roomResult['success']
        ? "🎉 管理员移除完成！\r✅ 管理员已移除\r房间ID: {$roomResult['room_id']}\r剩余管理员数量: {$managerCount}"
        : "🎉 管理员移除完成！\r❌ 操作失败: {$roomResult['message']}";
    }

    $sendResult = $this->sendGroupMessage($wxidRoom, $message, $port);

    if ($sendResult['success']) {
      $this->logger->info('Admin management completed for group: @group, Action: @action', [
        '@group' => $wxidRoom,
        '@action' => $action,
      ]);

      return [
        'status' => 'success',
        'message' => 'Admin management completed',
        'wxid_room' => $wxidRoom,
        'action' => $action,
        'room_result' => $roomResult,
        'send_result' => $sendResult,
      ];
    }

    return [
      'status' => 'error',
      'reason' => 'Failed to send completion message',
      'room_result' => $roomResult,
      'send_result' => $sendResult,
    ];
  }
}
