<?php

declare(strict_types=1);

namespace Drupal\lh\ValueObject;

use <PERSON><PERSON>al\lh\Enum\WagerRegion;

/**
 * Represents complete wager data.
 */
readonly class WagerData {

  /**
   * @param BetItem[] $bets
   */
  public function __construct(
    public WagerRegion $region,
    public string $wxidRoom,
    public string $wxid,
    public array $bets,
  ) {}

  /**
   * Gets the total amount for all bets.
   */
  public function getTotalAmount(): int {
    $total = 0;
    foreach ($this->bets as $bet) {
      if ($bet->isNumber()) {
        $total += $bet->amount;
      } elseif ($bet->isAnimal()) {
        // Animal bets are multiplied by the number of corresponding numbers
        $animalNumbers = $this->getAnimalNumbers($bet->value);
        $total += $bet->amount * count($animalNumbers);
      }
    }
    return $total;
  }

  /**
   * Gets all number bets.
   *
   * @return BetItem[]
   */
  public function getNumberBets(): array {
    return array_filter($this->bets, fn(BetItem $bet) => $bet->isNumber());
  }

  /**
   * Gets all animal bets.
   *
   * @return BetItem[]
   */
  public function getAnimalBets(): array {
    return array_filter($this->bets, fn(BetItem $bet) => $bet->isAnimal());
  }

  /**
   * Gets unique numbers from number bets.
   */
  public function getUniqueNumbers(): array {
    $numbers = [];
    foreach ($this->getNumberBets() as $bet) {
      $numbers[] = $bet->value;
    }
    return array_unique($numbers);
  }

  /**
   * Gets unique animals from animal bets.
   */
  public function getUniqueAnimals(): array {
    $animals = [];
    foreach ($this->getAnimalBets() as $bet) {
      $animals[] = $bet->value;
    }
    return array_unique($animals);
  }

  /**
   * Converts to array format for storage.
   */
  public function toArray(): array {
    return [
      'region' => $this->region->value,
      'wxid_room' => $this->wxidRoom,
      'wxid' => $this->wxid,
      'bets' => array_map(fn(BetItem $bet) => $bet->toArray(), $this->bets),
    ];
  }

  /**
   * Creates from array data.
   */
  public static function fromArray(array $data): self {
    $bets = array_map(
      fn(array $betData) => BetItem::fromArray($betData),
      $data['bets'] ?? []
    );

    return new self(
      WagerRegion::from($data['region']),
      $data['wxid_room'],
      $data['wxid'],
      $bets
    );
  }

  /**
   * Gets numbers for a given animal (2025 Snake Year mapping).
   */
  private function getAnimalNumbers(string $animal): array {
    $animalNumbers = [
      '蛇' => ['01', '13', '25', '37', '49'], // Snake year - 5 numbers
      '鼠' => ['06', '18', '30', '42'],
      '牛' => ['05', '17', '29', '41'],
      '虎' => ['04', '16', '28', '40'],
      '兔' => ['03', '15', '27', '39'],
      '龙' => ['02', '14', '26', '38'],
      '马' => ['12', '24', '36', '48'],
      '羊' => ['11', '23', '35', '47'],
      '猴' => ['10', '22', '34', '46'],
      '鸡' => ['09', '21', '33', '45'],
      '狗' => ['08', '20', '32', '44'],
      '猪' => ['07', '19', '31', '43'],
    ];

    return $animalNumbers[$animal] ?? [];
  }
}
