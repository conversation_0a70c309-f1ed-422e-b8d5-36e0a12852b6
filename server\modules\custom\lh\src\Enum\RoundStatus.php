<?php

declare(strict_types=1);

namespace Drupal\lh\Enum;

/**
 * Round status enumeration.
 */
enum RoundStatus: int {
  case IN_PROGRESS = 1;
  case COMPLETED = 2;

  /**
   * Gets the human-readable label.
   */
  public function getLabel(): string {
    return match($this) {
      self::IN_PROGRESS => '进行中',
      self::COMPLETED => '已完成',
    };
  }

  /**
   * Checks if the round is in progress.
   */
  public function isInProgress(): bool {
    return $this === self::IN_PROGRESS;
  }

  /**
   * Checks if the round is completed.
   */
  public function isCompleted(): bool {
    return $this === self::COMPLETED;
  }
}
