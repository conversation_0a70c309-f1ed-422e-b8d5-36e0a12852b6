<?php

declare(strict_types=1);

namespace Drupal\lh\Handler;

use <PERSON>upal\Core\Entity\EntityTypeManagerInterface;
use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Enum\RoundStatus;
use Drupal\lh\Service\GameManager;
use Drupal\lh\Service\WagerParser;
use Drupal\lh\Service\WechatApiClient;

/**
 * Handles wager commands.
 */
class WagerHandler extends AbstractMessageHandler {

  public function __construct(
    private readonly WagerParser $wagerParser,
    private readonly GameManager $gameManager,
    private readonly EntityTypeManagerInterface $entityTypeManager,
    WechatApiClient $wechatApiClient,
    LoggerChannelInterface $logger,
  ) {
    parent::__construct($wechatApiClient, $logger);
  }

  /**
   * {@inheritdoc}
   */
  public function canHandle(array $data): bool {
    $messageData = $data['data'] ?? [];
    
    if (!$this->isGroupMessage($messageData) || $this->isMessageFromSelf($messageData)) {
      return FALSE;
    }

    $message = $messageData['msg'] ?? '';
    return $this->wagerParser->isWagerCommand($message);
  }

  /**
   * {@inheritdoc}
   */
  public function handle(array $data): array {
    $messageData = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    $message = $messageData['msg'] ?? '';
    $wxidRoom = $messageData['fromWxid'] ?? '';
    $wxid = $messageData['finalFromWxid'] ?? '';

    // Check if game is started
    if (!$this->gameManager->isGameStarted($wxidRoom)) {
      $this->sendGroupMessage($wxidRoom, '游戏还没开始哦', $port);
      return ['status' => 'ignored', 'reason' => 'Game not started'];
    }

    // Parse wager command
    $wagerData = $this->wagerParser->parseWagerCommand($message, $wxidRoom, $wxid);
    if (!$wagerData) {
      $this->sendGroupMessage($wxidRoom, '下注格式不正确，请检查格式', $port);
      return ['status' => 'error', 'reason' => 'Invalid wager format'];
    }

    // Save wager to database
    $saveResult = $this->saveWager($wagerData, $message);
    if (!$saveResult['success']) {
      $this->sendGroupMessage($wxidRoom, '下注保存失败，请重试', $port);
      return ['status' => 'error', 'reason' => 'Failed to save wager', 'details' => $saveResult];
    }

    // Generate confirmation message
    $confirmationMessage = $this->generateConfirmationMessage($wagerData);
    $this->sendGroupMessage($wxidRoom, $confirmationMessage, $port);

    return [
      'status' => 'success',
      'message' => 'Wager processed successfully',
      'wager_id' => $saveResult['wager_id'],
      'wager_data' => $wagerData->toArray(),
    ];
  }

  /**
   * Saves wager data to database.
   */
  private function saveWager($wagerData, string $originalMessage): array {
    try {
      $storage = $this->entityTypeManager->getStorage('wager');
      
      $title = $this->generateWagerTitle($wagerData);
      
      $wager = $storage->create([
        'title' => $title,
        'wxid_room' => $wagerData->wxidRoom,
        'wxid' => $wagerData->wxid,
        'status' => 1,
        'status_round' => RoundStatus::IN_PROGRESS->value,
        'msg' => $originalMessage,
        'wager' => json_encode($wagerData->toArray()),
        'created' => time(),
      ]);

      if ($wager->save()) {
        return [
          'success' => TRUE,
          'wager_id' => $wager->id(),
          'message' => 'Wager saved successfully',
        ];
      }

      return [
        'success' => FALSE,
        'message' => 'Failed to save wager entity',
      ];
    } catch (\Exception $e) {
      $this->logger->error('Error saving wager: @error', ['@error' => $e->getMessage()]);
      return [
        'success' => FALSE,
        'message' => 'Exception occurred while saving wager',
        'error' => $e->getMessage(),
      ];
    }
  }

  /**
   * Generates a title for the wager.
   */
  private function generateWagerTitle($wagerData): string {
    $region = $wagerData->region->getLabel();
    $betCount = count($wagerData->bets);
    $totalAmount = $wagerData->getTotalAmount();
    
    $numberCount = count($wagerData->getNumberBets());
    $animalCount = count($wagerData->getAnimalBets());
    
    $description = [];
    if ($numberCount > 0) {
      $description[] = "{$numberCount}个号码";
    }
    if ($animalCount > 0) {
      $description[] = "{$animalCount}个生肖";
    }
    
    $descriptionText = implode('、', $description);
    
    return "{$region} {$betCount}注 {$totalAmount}元 {$descriptionText}";
  }

  /**
   * Generates confirmation message for the wager.
   */
  private function generateConfirmationMessage($wagerData): string {
    $region = $wagerData->region->getLabel();
    $totalAmount = $wagerData->getTotalAmount();
    
    $message = "✅ 下注确认\r\r";
    $message .= "🎯 地区: {$region}\r";
    $message .= "💰 总金额: {$totalAmount}元\r\r";
    
    // Number bets
    $numberBets = $wagerData->getNumberBets();
    if (!empty($numberBets)) {
      $message .= "🔢 号码下注:\r";
      foreach ($numberBets as $bet) {
        $message .= "  {$bet->value}号: {$bet->amount}元\r";
      }
      $message .= "\r";
    }
    
    // Animal bets
    $animalBets = $wagerData->getAnimalBets();
    if (!empty($animalBets)) {
      $message .= "🐾 生肖下注:\r";
      foreach ($animalBets as $bet) {
        $animalNumbers = $this->getAnimalNumbers($bet->value);
        $actualAmount = $bet->amount * count($animalNumbers);
        $message .= "  {$bet->value}: {$bet->amount}元 (实际{$actualAmount}元)\r";
      }
    }
    
    return $message;
  }

  /**
   * Gets numbers for a given animal (2025 Snake Year mapping).
   */
  private function getAnimalNumbers(string $animal): array {
    $animalNumbers = [
      '蛇' => ['01', '13', '25', '37', '49'], // Snake year - 5 numbers
      '鼠' => ['06', '18', '30', '42'],
      '牛' => ['05', '17', '29', '41'],
      '虎' => ['04', '16', '28', '40'],
      '兔' => ['03', '15', '27', '39'],
      '龙' => ['02', '14', '26', '38'],
      '马' => ['12', '24', '36', '48'],
      '羊' => ['11', '23', '35', '47'],
      '猴' => ['10', '22', '34', '46'],
      '鸡' => ['09', '21', '33', '45'],
      '狗' => ['08', '20', '32', '44'],
      '猪' => ['07', '19', '31', '43'],
    ];

    return $animalNumbers[$animal] ?? [];
  }
}
