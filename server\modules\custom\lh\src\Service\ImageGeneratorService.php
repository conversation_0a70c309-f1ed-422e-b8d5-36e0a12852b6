<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Enum\WagerRegion;

/**
 * Service for generating images from data.
 */
class ImageGeneratorService {

  private const IMAGE_WIDTH = 500;
  private const IMAGE_HEIGHT = 600;
  private const PADDING = 20;
  private const LINE_HEIGHT = 25;
  private const FONT_SIZE_TITLE = 16;
  private const FONT_SIZE_NORMAL = 12;
  private const FONT_SIZE_SMALL = 10;

  public function __construct(
    private readonly FileSystemInterface $fileSystem,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Generates statistics image.
   */
  public function generateStatisticsImage(array $statistics): ?string {
    try {
      // Create image
      $image = imagecreatetruecolor(self::IMAGE_WIDTH, self::IMAGE_HEIGHT);
      if (!$image) {
        return NULL;
      }

      // Define colors
      $colors = $this->defineColors($image);

      // Fill background
      imagefill($image, 0, 0, $colors['background']);

      // Draw border
      $this->drawBorder($image, $colors);

      // Draw content
      $y = $this->drawStatisticsContent($image, $statistics, $colors);

      // Adjust image height if needed
      if ($y < self::IMAGE_HEIGHT - self::PADDING) {
        $newImage = imagecreatetruecolor(self::IMAGE_WIDTH, $y + self::PADDING);
        imagefill($newImage, 0, 0, $colors['background']);
        imagecopy($newImage, $image, 0, 0, 0, 0, self::IMAGE_WIDTH, $y + self::PADDING);
        imagedestroy($image);
        $image = $newImage;
        $this->drawBorder($image, $colors, $y + self::PADDING);
      }

      // Save image
      $filename = $this->saveImage($image);
      imagedestroy($image);

      return $filename;
    } catch (\Exception $e) {
      $this->logger->error('Error generating statistics image: @error', ['@error' => $e->getMessage()]);
      return NULL;
    }
  }

  /**
   * Defines color palette.
   */
  private function defineColors($image): array {
    return [
      'background' => imagecolorallocate($image, 248, 249, 250),
      'border' => imagecolorallocate($image, 220, 221, 222),
      'title' => imagecolorallocate($image, 24, 144, 255),
      'text' => imagecolorallocate($image, 51, 51, 51),
      'secondary' => imagecolorallocate($image, 102, 102, 102),
      'success' => imagecolorallocate($image, 82, 196, 26),
      'warning' => imagecolorallocate($image, 250, 173, 20),
      'macau' => imagecolorallocate($image, 255, 77, 79),
      'hongkong' => imagecolorallocate($image, 24, 144, 255),
    ];
  }

  /**
   * Draws border around the image.
   */
  private function drawBorder($image, array $colors, ?int $height = NULL): void {
    $h = $height ?? self::IMAGE_HEIGHT;
    imagerectangle($image, 0, 0, self::IMAGE_WIDTH - 1, $h - 1, $colors['border']);
    imagerectangle($image, 1, 1, self::IMAGE_WIDTH - 2, $h - 2, $colors['border']);
  }

  /**
   * Draws statistics content.
   */
  private function drawStatisticsContent($image, array $statistics, array $colors): int {
    $y = self::PADDING + 10;

    // Title
    $y = $this->drawText($image, '📊 当前统计', self::PADDING, $y, $colors['title'], self::FONT_SIZE_TITLE, true);
    $y += 10;

    // Check if no data
    if ($statistics['total_wagers'] === 0) {
      $y = $this->drawText($image, '暂无下注记录', self::PADDING, $y, $colors['secondary'], self::FONT_SIZE_NORMAL);
      return $y + 20;
    }

    // Summary
    $y = $this->drawText($image, "📝 总下注数: {$statistics['total_wagers']}", self::PADDING, $y, $colors['text'], self::FONT_SIZE_NORMAL);
    $y = $this->drawText($image, "💰 总金额: {$statistics['total_amount']}元", self::PADDING, $y, $colors['success'], self::FONT_SIZE_NORMAL, true);
    $y += 10;

    // Region statistics
    if (!empty($statistics['regions'])) {
      foreach ($statistics['regions'] as $region => $data) {
        $regionEnum = WagerRegion::from($region);
        $emoji = $regionEnum->getEmoji();
        $label = $regionEnum->getLabel();
        $color = $region === 'macau' ? $colors['macau'] : $colors['hongkong'];
        
        $text = "{$emoji} {$label}: {$data['count']}注 {$data['amount']}元";
        $y = $this->drawText($image, $text, self::PADDING, $y, $color, self::FONT_SIZE_NORMAL);
      }
      $y += 10;
    }

    // Number statistics
    if (!empty($statistics['numbers'])) {
      $y = $this->drawText($image, '🔢 号码统计:', self::PADDING, $y, $colors['title'], self::FONT_SIZE_NORMAL, true);
      
      // Sort numbers and get top 10
      arsort($statistics['numbers']);
      $topNumbers = array_slice($statistics['numbers'], 0, 10, true);
      
      foreach ($topNumbers as $number => $amount) {
        $barWidth = min(200, ($amount / max($statistics['numbers'])) * 200);
        $this->drawProgressBar($image, self::PADDING + 20, $y - 5, $barWidth, $colors['warning']);
        $text = "{$number}号: {$amount}元";
        $y = $this->drawText($image, $text, self::PADDING + 20, $y, $colors['text'], self::FONT_SIZE_SMALL);
      }
      $y += 10;
    }

    // Animal statistics
    if (!empty($statistics['animals'])) {
      $y = $this->drawText($image, '🐾 生肖统计:', self::PADDING, $y, $colors['title'], self::FONT_SIZE_NORMAL, true);
      
      foreach ($statistics['animals'] as $animal => $data) {
        $text = "{$animal}: {$data['display_amount']}元 (实际{$data['actual_amount']}元)";
        $y = $this->drawText($image, $text, self::PADDING + 20, $y, $colors['text'], self::FONT_SIZE_SMALL);
      }
    }

    return $y;
  }

  /**
   * Draws text on image.
   */
  private function drawText($image, string $text, int $x, int $y, int $color, int $fontSize, bool $bold = false): int {
    // Convert emoji and special characters to simpler representation for GD
    $text = $this->convertEmojisForGD($text);
    
    if ($bold) {
      // Simulate bold by drawing text multiple times with slight offset
      imagestring($image, $fontSize > 12 ? 5 : 3, $x, $y, $text, $color);
      imagestring($image, $fontSize > 12 ? 5 : 3, $x + 1, $y, $text, $color);
    } else {
      imagestring($image, $fontSize > 12 ? 5 : 3, $x, $y, $text, $color);
    }
    
    return $y + self::LINE_HEIGHT;
  }

  /**
   * Draws a progress bar.
   */
  private function drawProgressBar($image, int $x, int $y, int $width, int $color): void {
    imagefilledrectangle($image, $x, $y, $x + $width, $y + 8, $color);
  }

  /**
   * Converts emojis to text representations for GD compatibility.
   */
  private function convertEmojisForGD(string $text): string {
    $replacements = [
      '📊' => '[统计]',
      '📝' => '[记录]',
      '💰' => '[金额]',
      '🇲🇴' => '[澳门]',
      '🇭🇰' => '[香港]',
      '🔢' => '[号码]',
      '🐾' => '[生肖]',
      '✅' => '[确认]',
      '🎯' => '[地区]',
    ];

    return str_replace(array_keys($replacements), array_values($replacements), $text);
  }

  /**
   * Saves image to temporary directory.
   */
  private function saveImage($image): string {
    $tempDir = $this->fileSystem->getTempDirectory();
    $filename = 'lh_statistics_' . time() . '_' . rand(1000, 9999) . '.png';
    $filepath = $tempDir . '/' . $filename;
    
    imagepng($image, $filepath);
    
    return $filepath;
  }
}
