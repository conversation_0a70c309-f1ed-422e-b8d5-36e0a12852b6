<?php

declare(strict_types=1);

namespace Drupal\lh\Plugin\rest\resource;

use <PERSON><PERSON>al\Core\StringTranslation\TranslatableMarkup;
use <PERSON><PERSON>al\lh\Service\MessageDispatcher;
use Drupal\rest\Attribute\RestResource;
use <PERSON><PERSON>al\rest\ModifiedResourceResponse;
use Drupal\rest\Plugin\ResourceBase;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

#[RestResource(
  id: "qx",
  label: new TranslatableMarkup("QX"),
  uri_paths: ["create" => "/api/qx"]
)]
class QxResource extends ResourceBase {

  public function __construct(
    array $configuration,
    $plugin_id,
    $plugin_definition,
    $serializer_formats,
    LoggerInterface $logger,
    protected readonly MessageDispatcher $messageDispatcher
  ) {
    parent::__construct($configuration, $plugin_id, $plugin_definition, $serializer_formats, $logger);
  }

  public static function create(ContainerInterface $container, array $configuration, $plugin_id, $plugin_definition): static {
    return new static(
      $configuration,
      $plugin_id,
      $plugin_definition,
      $container->getParameter('serializer.formats'),
      $container->get('logger.factory')->get('rest'),
      $container->get('lh.message_dispatcher')
    );
  }

  /**
   * Processes WeChat callback events.
   */
  public function post(array $data = []): ModifiedResourceResponse {
    if (empty($data['type'])) {
      return new ModifiedResourceResponse(['error' => 'Missing type parameter'], 400);
    }

    $event_type = $data['type'];
    $result = match ($event_type) {
      'injectSuccess' => $this->messageDispatcher->handleInjectSuccess($data),
      'loginSuccess' => $this->messageDispatcher->handleLoginSuccess($data),
      'recvMsg' => $this->messageDispatcher->handleRecvMsg($data),
      'transPay' => $this->messageDispatcher->handleTransPay($data),
      'revokeMsg' => $this->messageDispatcher->handleRevokeMsg($data),
      'friendReq' => $this->messageDispatcher->handleFriendReq($data),
      'authExpire' => $this->messageDispatcher->handleAuthExpire($data),
      'qrPay' => $this->messageDispatcher->handleQrPay($data),
      'groupMemberChanges' => $this->messageDispatcher->handleGroupMemberChanges($data),
      default => ['status' => 'ignored', 'message' => 'Unknown event type'],
    };

    return new ModifiedResourceResponse($result, 200);
  }

}
