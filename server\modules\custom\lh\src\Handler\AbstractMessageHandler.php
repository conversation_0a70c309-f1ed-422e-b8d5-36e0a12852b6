<?php

declare(strict_types=1);

namespace Drupal\lh\Handler;

use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Service\WechatApiClient;

/**
 * Abstract base class for message handlers.
 */
abstract class AbstractMessageHandler implements MessageHandlerInterface {

  public function __construct(
    protected readonly WechatApiClient $wechatApiClient,
    protected readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Checks if the message is from a group.
   */
  protected function isGroupMessage(array $messageData): bool {
    return ($messageData['fromType'] ?? 0) === 2;
  }

  /**
   * Gets the current WeChat account information.
   */
  protected function getCurrentWechatInfo(): ?array {
    return $this->wechatApiClient->getSelfInfo();
  }

  /**
   * Checks if message is sent by current WeChat account.
   */
  protected function isMessageFromSelf(array $messageData): bool {
    $info = $this->getCurrentWechatInfo();
    $currentWxid = $info['result']['wxid'] ?? NULL;
    $finalFromWxid = $messageData['finalFromWxid'] ?? '';

    return $currentWxid && $finalFromWxid === $currentWxid;
  }

  /**
   * Sends message to WeChat group.
   */
  protected function sendGroupMessage(string $wxidRoom, string $message, int $port = 6999, bool $isAutoReply = TRUE): array {
    $this->wechatApiClient->setBaseUrl("http://127.0.0.1:{$port}/wechat/httpapi");
    $finalMessage = $isAutoReply ? "哎哟，{$message}" : $message;
    $result = $this->wechatApiClient->sendText($wxidRoom, $finalMessage);

    return [
      'success' => $result && isset($result['code']) && $result['code'] === 200,
      'message' => $result && isset($result['code']) && $result['code'] === 200 ? 'Message sent successfully' : 'Failed to send message',
      'send_result' => $result,
    ];
  }

  /**
   * Gets trigger keywords configuration.
   */
  protected function getTriggerKeywords(): array {
    return [
      'admin_set' => ['设置管理员'],
      'admin_remove' => ['移除管理员'],
      'game_start' => ['开始', '开机', '开局', '开搞', '开工'],
      'game_end' => ['收工', '结束', '下班'],
      'statistics' => ['统计'],
    ];
  }

  /**
   * Checks if message exactly matches any keywords from specified category.
   */
  protected function containsKeywords(string $message, string $category): bool {
    $keywords = $this->getTriggerKeywords();

    if (!isset($keywords[$category])) {
      return FALSE;
    }

    $cleanMessage = trim($message);

    foreach ($keywords[$category] as $keyword) {
      if ($cleanMessage === $keyword) {
        return TRUE;
      }
    }

    return FALSE;
  }

  /**
   * Checks if message is an admin command, supporting @ mentions.
   */
  protected function isAdminCommand(string $message, string $category): bool {
    $keywords = $this->getTriggerKeywords();

    if (!isset($keywords[$category])) {
      return FALSE;
    }

    $cleanMessage = trim($message);

    foreach ($keywords[$category] as $keyword) {
      // Exact match
      if ($cleanMessage === $keyword) {
        return TRUE;
      }

      // Remove @ mentions and check
      $messageWithoutAt = preg_replace('/@\S+/u', '', $cleanMessage);
      $messageWithoutAt = preg_replace('/\s+/', ' ', trim($messageWithoutAt));

      if ($messageWithoutAt === $keyword) {
        return TRUE;
      }

      // Match format: @username keyword or @username @username keyword
      if (preg_match('/^(@\S+\s+)+' . preg_quote($keyword, '/') . '$/u', $cleanMessage)) {
        return TRUE;
      }
    }

    return FALSE;
  }
}
