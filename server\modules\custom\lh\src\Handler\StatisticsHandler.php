<?php

declare(strict_types=1);

namespace Drupal\lh\Handler;

use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Service\StatisticsService;
use Drupal\lh\Service\WechatApiClient;

/**
 * Handles statistics commands.
 */
class StatisticsHandler extends AbstractMessageHandler {

  public function __construct(
    private readonly StatisticsService $statisticsService,
    WechatApiClient $wechatApiClient,
    LoggerChannelInterface $logger,
  ) {
    parent::__construct($wechatApiClient, $logger);
  }

  /**
   * {@inheritdoc}
   */
  public function canHandle(array $data): bool {
    $messageData = $data['data'] ?? [];
    
    if (!$this->isGroupMessage($messageData)) {
      return FALSE;
    }

    $message = $messageData['msg'] ?? '';
    return $this->containsKeywords($message, 'statistics');
  }

  /**
   * {@inheritdoc}
   */
  public function handle(array $data): array {
    $messageData = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    $message = $messageData['msg'] ?? '';
    $wxidRoom = $messageData['fromWxid'] ?? '';
    $wxid = $messageData['finalFromWxid'] ?? '';

    if (!$this->containsKeywords($message, 'statistics')) {
      return ['status' => 'ignored', 'reason' => 'Not a statistics command'];
    }

    // Check if user is a manager (only managers can view statistics)
    if (!$this->statisticsService->isManager($wxidRoom, $wxid)) {
      return ['status' => 'ignored', 'reason' => 'Not a manager'];
    }

    $statistics = $this->statisticsService->generateStatistics($wxidRoom);
    
    // Send statistics as image
    $sendResult = $this->statisticsService->sendStatisticsImage($wxidRoom, $statistics, $port);
    
    // If image sending failed, send as text
    if (!$sendResult['success'] && isset($sendResult['fallback_text'])) {
      $this->sendGroupMessage($wxidRoom, $sendResult['fallback_text'], $port);
    }

    return [
      'status' => 'success',
      'message' => 'Statistics generated and sent',
      'statistics' => $statistics,
      'send_result' => $sendResult,
    ];
  }
}
