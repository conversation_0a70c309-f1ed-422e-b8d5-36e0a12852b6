<?php

declare(strict_types=1);

namespace Drupal\lh\Handler;

use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Enum\GameStatus;
use Drupal\lh\Service\GameManager;
use Drupal\lh\Service\WechatApiClient;

/**
 * Handles game control commands from managers.
 */
class GameControlHandler extends AbstractMessageHandler {

  public function __construct(
    private readonly GameManager $gameManager,
    WechatApiClient $wechatApiClient,
    LoggerChannelInterface $logger,
  ) {
    parent::__construct($wechatApiClient, $logger);
  }

  /**
   * {@inheritdoc}
   */
  public function canHandle(array $data): bool {
    $messageData = $data['data'] ?? [];
    
    if (!$this->isGroupMessage($messageData)) {
      return FALSE;
    }

    $message = $messageData['msg'] ?? '';
    $wxidRoom = $messageData['fromWxid'] ?? '';
    $senderWxid = $messageData['finalFromWxid'] ?? '';

    if (empty($wxidRoom) || empty($senderWxid)) {
      return FALSE;
    }

    if (!$this->gameManager->isManager($wxidRoom, $senderWxid)) {
      return FALSE;
    }

    return $this->containsKeywords($message, 'game_start') || $this->containsKeywords($message, 'game_end');
  }

  /**
   * {@inheritdoc}
   */
  public function handle(array $data): array {
    $messageData = $data['data'] ?? [];
    $port = $data['port'] ?? 6999;

    $message = $messageData['msg'] ?? '';
    $wxidRoom = $messageData['fromWxid'] ?? '';
    $senderWxid = $messageData['finalFromWxid'] ?? '';

    $isStartCommand = $this->containsKeywords($message, 'game_start');
    $isEndCommand = $this->containsKeywords($message, 'game_end');

    if (!$isStartCommand && !$isEndCommand) {
      return ['status' => 'ignored', 'reason' => 'Not a valid game control command'];
    }

    // Update game status
    $newStatus = $isStartCommand ? GameStatus::RUNNING : GameStatus::STOPPED;
    $statusResult = $this->gameManager->updateGameStatus($wxidRoom, $newStatus);

    if (!$statusResult['success']) {
      return [
        'status' => 'error',
        'reason' => 'Failed to update game status',
        'result' => $statusResult,
      ];
    }

    // Send response message
    $action = $isStartCommand ? '开始' : '结束';
    $responseMessage = $isStartCommand ? '请开始吧' : '结束了哦';
    $sendResult = $this->sendGroupMessage($wxidRoom, $responseMessage, $port);

    if ($sendResult['success']) {
      $this->logger->info('Game @action command executed by manager: @sender in group: @group', [
        '@action' => $action,
        '@sender' => $senderWxid,
        '@group' => $wxidRoom,
      ]);

      return [
        'status' => 'success',
        'message' => "Game {$action} command executed",
        'wxid_room' => $wxidRoom,
        'sender_wxid' => $senderWxid,
        'game_status' => $newStatus,
        'send_result' => $sendResult,
      ];
    }

    return [
      'status' => 'error',
      'reason' => 'Failed to send response message',
      'send_result' => $sendResult,
    ];
  }
}
