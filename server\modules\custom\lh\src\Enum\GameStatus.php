<?php

declare(strict_types=1);

namespace Drupal\lh\Enum;

/**
 * Game status enumeration.
 */
enum GameStatus: int {
  case STOPPED = 0;
  case RUNNING = 1;

  /**
   * Gets the human-readable label.
   */
  public function getLabel(): string {
    return match($this) {
      self::STOPPED => '游戏已结束',
      self::RUNNING => '游戏进行中',
    };
  }

  /**
   * Checks if the game is running.
   */
  public function isRunning(): bool {
    return $this === self::RUNNING;
  }

  /**
   * Checks if the game is stopped.
   */
  public function isStopped(): bool {
    return $this === self::STOPPED;
  }
}
