<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Enum\GameStatus;
use Drupal\lh\Enum\RoundStatus;

/**
 * Manages game state and room operations.
 */
class GameManager {

  public function __construct(
    private readonly EntityTypeManagerInterface $entityTypeManager,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Checks if a user is a manager of the room.
   */
  public function isManager(string $wxidRoom, string $wxid): bool {
    $storage = $this->entityTypeManager->getStorage('room');
    $rooms = $storage->loadByProperties(['title' => $wxidRoom]);

    if (empty($rooms)) {
      return FALSE;
    }

    $room = reset($rooms);
    $createdWxid = $room->get('created_wxid')->value ?? '';

    // C<PERSON> is always a manager
    if ($createdWxid === $wxid) {
      return TRUE;
    }

    // Check if user is in manager list
    $managerWxids = json_decode($room->get('manager_wxid')->value ?? '[]', TRUE) ?: [];
    return in_array($wxid, $managerWxids);
  }

  /**
   * Checks if the game is started in a room.
   */
  public function isGameStarted(string $wxidRoom): bool {
    $storage = $this->entityTypeManager->getStorage('room');
    $rooms = $storage->loadByProperties(['title' => $wxidRoom]);

    if (empty($rooms)) {
      return FALSE;
    }

    $room = reset($rooms);
    $gameStatus = $room->get('status_game')->value ?? 0;
    return GameStatus::from($gameStatus)->isRunning();
  }

  /**
   * Updates the game status for a room.
   */
  public function updateGameStatus(string $wxidRoom, GameStatus $status): array {
    $storage = $this->entityTypeManager->getStorage('room');
    $rooms = $storage->loadByProperties(['title' => $wxidRoom]);

    if (empty($rooms)) {
      return [
        'success' => FALSE,
        'message' => '房间不存在',
        'action' => 'room_not_found',
      ];
    }

    $room = reset($rooms);
    $oldStatus = GameStatus::from($room->get('status_game')->value ?? 0);

    // Update game status
    $room->set('status_game', $status->value);
    $room->set('changed', time());

    if ($room->save()) {
      $result = [
        'success' => TRUE,
        'message' => '游戏状态已更新',
        'action' => 'status_updated',
        'room_id' => $room->id(),
        'old_status' => $oldStatus,
        'new_status' => $status,
      ];

      // If game is stopped, update all wager round status to completed
      if ($status->isStopped()) {
        $wagerUpdateResult = $this->updateWagerRoundStatus($wxidRoom, RoundStatus::COMPLETED);
        $result['wager_update'] = $wagerUpdateResult;
      }

      return $result;
    }

    return [
      'success' => FALSE,
      'message' => '游戏状态更新失败',
      'action' => 'update_failed',
    ];
  }

  /**
   * Creates or updates a room with manager information.
   */
  public function createOrUpdateRoom(string $wxidRoom, string $createdWxid, array $managerWxids = []): array {
    $storage = $this->entityTypeManager->getStorage('room');
    $existingRooms = $storage->loadByProperties(['title' => $wxidRoom]);

    if (!empty($existingRooms)) {
      $room = reset($existingRooms);
      $room->set('created_wxid', $createdWxid);

      if (!empty($managerWxids)) {
        $existingManagers = json_decode($room->get('manager_wxid')->value ?? '[]', TRUE) ?: [];
        $mergedManagers = array_values(array_unique(array_merge($existingManagers, $managerWxids)));
        $room->set('manager_wxid', json_encode($mergedManagers));
      }

      $room->set('changed', time());

      return $room->save() ? [
        'success' => TRUE,
        'action' => 'updated',
        'message' => '房间信息已更新',
        'room_id' => $room->id(),
        'managers' => $mergedManagers ?? [],
      ] : [
        'success' => FALSE,
        'action' => 'update_failed',
        'message' => '房间更新失败',
      ];
    }

    $room = $storage->create([
      'title' => $wxidRoom,
      'created_wxid' => $createdWxid,
      'manager_wxid' => json_encode($managerWxids),
    ]);

    return $room->save() ? [
      'success' => TRUE,
      'action' => 'created',
      'message' => '房间创建成功',
      'room_id' => $room->id(),
      'managers' => $managerWxids,
    ] : [
      'success' => FALSE,
      'action' => 'create_failed',
      'message' => '房间创建失败',
    ];
  }

  /**
   * Removes managers from a room.
   */
  public function removeManagersFromRoom(string $wxidRoom, array $managerWxids): array {
    $storage = $this->entityTypeManager->getStorage('room');
    $existingRooms = $storage->loadByProperties(['title' => $wxidRoom]);

    if (empty($existingRooms)) {
      return [
        'success' => FALSE,
        'action' => 'room_not_found',
        'message' => '房间不存在',
      ];
    }

    $room = reset($existingRooms);
    $existingManagers = json_decode($room->get('manager_wxid')->value ?? '[]', TRUE) ?: [];
    $updatedManagers = array_values(array_diff($existingManagers, $managerWxids));

    $room->set('manager_wxid', json_encode($updatedManagers));
    $room->set('changed', time());

    return $room->save() ? [
      'success' => TRUE,
      'action' => 'managers_removed',
      'message' => '管理员已移除',
      'room_id' => $room->id(),
      'managers' => $updatedManagers,
    ] : [
      'success' => FALSE,
      'action' => 'remove_failed',
      'message' => '移除管理员失败',
    ];
  }

  /**
   * Updates wager round status for all wagers in a room.
   */
  private function updateWagerRoundStatus(string $wxidRoom, RoundStatus $statusRound): array {
    $storage = $this->entityTypeManager->getStorage('wager');
    if (!$storage) {
      return [
        'success' => FALSE,
        'message' => '无法获取wager存储服务',
        'updated_count' => 0,
      ];
    }

    $wagers = $storage->loadByProperties(['wxid_room' => $wxidRoom]);
    if (empty($wagers)) {
      return [
        'success' => TRUE,
        'message' => '当前群组没有需要更新的wager记录',
        'updated_count' => 0,
      ];
    }

    $updatedCount = 0;
    foreach ($wagers as $wager) {
      $wager->set('status_round', $statusRound->value);
      $wager->set('changed', time());
      if ($wager->save()) {
        $updatedCount++;
      }
    }

    return [
      'success' => TRUE,
      'message' => "已更新{$updatedCount}条wager记录的轮次状态",
      'updated_count' => $updatedCount,
      'target_status' => $statusRound,
    ];
  }
}
