<?php

declare(strict_types=1);

namespace Drupal\lh\Enum;

/**
 * Wager region enumeration.
 */
enum WagerRegion: string {
  case MACAU = 'macau';
  case HONGKONG = 'hongkong';

  /**
   * Gets the human-readable label.
   */
  public function getLabel(): string {
    return match($this) {
      self::MACAU => '澳门',
      self::HONGKONG => '香港',
    };
  }

  /**
   * Gets the emoji representation.
   */
  public function getEmoji(): string {
    return match($this) {
      self::MACAU => '🇲🇴',
      self::HONGKONG => '🇭🇰',
    };
  }

  /**
   * Detects region from message text.
   */
  public static function detectFromMessage(string $message): self {
    if (preg_match('/香港|🇭🇰/u', $message)) {
      return self::HONGKONG;
    }
    
    return self::MACAU; // Default to Macau
  }

  /**
   * Gets all available regions.
   */
  public static function getAll(): array {
    return [self::MACAU, self::HONGKONG];
  }
}
