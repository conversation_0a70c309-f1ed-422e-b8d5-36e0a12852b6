<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\Logger\LoggerChannelInterface;

/**
 * Service for sending images via WeChat API.
 */
class WechatImageSender {

  public function __construct(
    private readonly WechatApiClient $wechatApiClient,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Sends an image to WeChat group.
   */
  public function sendImage(string $wxidRoom, string $imagePath, int $port = 6999): array {
    try {
      // Set the API base URL
      $this->wechatApiClient->setBaseUrl("http://127.0.0.1:{$port}/wechat/httpapi");

      // Check if file exists
      if (!file_exists($imagePath)) {
        return [
          'success' => FALSE,
          'message' => 'Image file not found',
          'path' => $imagePath,
        ];
      }

      // Send image via WeChat API
      $result = $this->wechatApiClient->sendImage($wxidRoom, $imagePath);

      // Clean up temporary file
      $this->cleanupTempFile($imagePath);

      if ($result && isset($result['code']) && $result['code'] === 200) {
        return [
          'success' => TRUE,
          'message' => 'Image sent successfully',
          'api_result' => $result,
        ];
      }

      return [
        'success' => FALSE,
        'message' => 'Failed to send image via WeChat API',
        'api_result' => $result,
      ];

    } catch (\Exception $e) {
      $this->logger->error('Error sending image: @error', ['@error' => $e->getMessage()]);
      
      // Clean up on error
      $this->cleanupTempFile($imagePath);
      
      return [
        'success' => FALSE,
        'message' => 'Exception occurred while sending image',
        'error' => $e->getMessage(),
      ];
    }
  }

  /**
   * Sends image with fallback to text message.
   */
  public function sendImageWithFallback(string $wxidRoom, string $imagePath, string $fallbackText, int $port = 6999): array {
    $imageResult = $this->sendImage($wxidRoom, $imagePath, $port);
    
    if ($imageResult['success']) {
      return $imageResult;
    }

    // Fallback to text message
    $this->logger->warning('Image send failed, falling back to text message');
    
    $textResult = $this->wechatApiClient->sendText($wxidRoom, "哎哟，{$fallbackText}");
    
    return [
      'success' => $textResult && isset($textResult['code']) && $textResult['code'] === 200,
      'message' => 'Sent as text fallback',
      'image_error' => $imageResult,
      'text_result' => $textResult,
    ];
  }

  /**
   * Cleans up temporary image file.
   */
  private function cleanupTempFile(string $imagePath): void {
    try {
      if (file_exists($imagePath) && strpos($imagePath, 'lh_statistics_') !== FALSE) {
        unlink($imagePath);
      }
    } catch (\Exception $e) {
      $this->logger->warning('Failed to cleanup temp file: @file, Error: @error', [
        '@file' => $imagePath,
        '@error' => $e->getMessage(),
      ]);
    }
  }
}
