<?php

declare(strict_types=1);

namespace Drupal\lh\ValueObject;

/**
 * Represents a single bet item.
 */
readonly class BetItem {

  public function __construct(
    public string $type,
    public string $value,
    public int $amount,
    public string $source = 'direct',
  ) {}

  /**
   * Creates a number bet.
   */
  public static function createNumber(string $number, int $amount): self {
    return new self('number', $number, $amount, 'direct');
  }

  /**
   * Creates an animal bet.
   */
  public static function createAnimal(string $animal, int $amount): self {
    return new self('animal', $animal, $amount, 'direct');
  }

  /**
   * Checks if this is a number bet.
   */
  public function isNumber(): bool {
    return $this->type === 'number';
  }

  /**
   * Checks if this is an animal bet.
   */
  public function isAnimal(): bool {
    return $this->type === 'animal';
  }

  /**
   * Converts to array format for storage.
   */
  public function toArray(): array {
    return [
      'type' => $this->type,
      'value' => $this->value,
      'amount' => $this->amount,
      'source' => $this->source,
    ];
  }

  /**
   * Creates from array data.
   */
  public static function fromArray(array $data): self {
    return new self(
      $data['type'],
      $data['value'],
      $data['amount'],
      $data['source'] ?? 'direct'
    );
  }
}
