<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\Logger\LoggerChannelInterface;
use Drupal\lh\Enum\WagerRegion;
use Drupal\lh\ValueObject\BetItem;
use Drupal\lh\ValueObject\WagerData;

/**
 * Parses wager commands from messages.
 */
class WagerParser {

  private const ANIMALS = ['羊', '牛', '虎', '猴', '鼠', '兔', '马', '蛇', '龙', '狗', '鸡', '猪'];

  public function __construct(
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Checks if a message is a wager command.
   */
  public function isWagerCommand(string $message): bool {
    $cleanMessage = preg_replace('/澳门|香港|🇲🇴|🇭🇰/u', '', $message);
    $cleanMessage = preg_replace('/\s+/', ' ', trim($cleanMessage));

    if (!preg_match('/\d/', $cleanMessage)) {
      return FALSE;
    }

    $hasNumbers = (bool) preg_match('/\b(?:0[1-9]|[1-4][0-9])\b/', $cleanMessage);
    $hasAnimals = (bool) preg_match('/[羊牛虎猴鼠兔马蛇龙狗鸡猪]/u', $cleanMessage);

    if (!$hasNumbers && !$hasAnimals) {
      return FALSE;
    }

    return (bool) preg_match('/各(?:数)?\s*\d+/', $cleanMessage);
  }

  /**
   * Parses a wager command into WagerData.
   */
  public function parseWagerCommand(string $message, string $wxidRoom, string $wxid): ?WagerData {
    if (!$this->isWagerCommand($message)) {
      return NULL;
    }

    $region = WagerRegion::detectFromMessage($message);
    $cleanMessage = preg_replace('/澳门|香港|🇲🇴|🇭🇰/u', '', $message);
    $lines = preg_split('/[\r\n]+/', trim($cleanMessage));
    
    $betLines = [];
    $amountInfo = NULL;
    $bets = [];

    foreach ($lines as $line) {
      $line = trim($line);
      if (empty($line)) {
        continue;
      }

      // Process lines with "各" keyword and amount
      if (preg_match('/(.+?)各(?:数)?\s*(\d+)([米元])?/u', $line, $matches)) {
        $itemsText = $matches[1];
        $itemsText = preg_replace('/[\x{2014}\x{2013}\x{002D}—-]+\.?$/u', '', $itemsText);
        $itemsText = preg_replace('/\.+$/', '', $itemsText);
        $amount = (int)$matches[2];
        $this->processBetItems($itemsText, $amount, $bets);
        continue;
      }

      // Process single item with amount
      if (preg_match('/^([羊牛虎猴鼠兔马蛇龙狗鸡猪]|\d{1,2})\s*[-—]?\s*(\d+)([米元])?$/u', $line, $matches)) {
        $item = $matches[1];
        $amount = (int)$matches[2];
        $this->processBetItems($item, $amount, $bets);
        continue;
      }

      // Process animals with amount
      if (preg_match('/([羊牛虎猴鼠兔马蛇龙狗鸡猪]+).*?(\d+)([米元])?/u', $line, $matches)) {
        $animalsText = $matches[1];
        $amount = (int)$matches[2];
        $this->processBetItems($animalsText, $amount, $bets);
        continue;
      }

      // Store amount info for later application
      if (preg_match('/^\s*各(?:数)?.*?(\d+)([米元])?$/u', $line, $matches)) {
        $amountInfo = [
          'amount' => (int)$matches[1],
          'unit' => $matches[2] ?? '',
        ];
        continue;
      }

      // Store lines with numbers but no animals for later processing
      if (preg_match('/\d/', $line) && !preg_match('/[羊牛虎猴鼠兔马蛇龙狗鸡猪]/', $line)) {
        $betLines[] = $line;
      }
    }

    // Apply stored amount to bet lines
    if (!empty($betLines) && $amountInfo) {
      foreach ($betLines as $betLine) {
        $this->processBetItems($betLine, $amountInfo['amount'], $bets);
      }
    }

    if (empty($bets)) {
      return NULL;
    }

    return new WagerData($region, $wxidRoom, $wxid, $bets);
  }

  /**
   * Processes bet items from text and adds them to the bets array.
   */
  private function processBetItems(string $itemsText, int $amount, array &$bets): void {
    // Process numbers
    $numbers = $this->extractNumbers($itemsText);
    foreach ($numbers as $number) {
      $bets[] = BetItem::createNumber($number, $amount);
    }

    // Process animals
    $animals = $this->extractAnimals($itemsText);
    foreach ($animals as $animal) {
      $bets[] = BetItem::createAnimal($animal, $amount);
    }
  }

  /**
   * Extracts numbers from text.
   */
  private function extractNumbers(string $text): array {
    preg_match_all('/\b([0-4]?[0-9])\b/', $text, $matches);
    $numbers = array_unique($matches[1]);
    $formattedNumbers = [];
    
    foreach ($numbers as $number) {
      $num = (int)$number;
      if ($num >= 1 && $num <= 49) {
        $formattedNumbers[] = str_pad((string)$num, 2, '0', STR_PAD_LEFT);
      }
    }
    
    return array_unique($formattedNumbers);
  }

  /**
   * Extracts animals from text.
   */
  private function extractAnimals(string $text): array {
    $found = [];
    
    foreach (self::ANIMALS as $animal) {
      if (strpos($text, $animal) !== FALSE) {
        $found[] = $animal;
      }
    }
    
    return $found;
  }
}
