<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use Drupal\Core\Entity\EntityTypeManagerInterface;
use Drupal\lh\Enum\RoundStatus;
use Drupal\lh\Enum\WagerRegion;
use Drupal\lh\ValueObject\WagerData;

/**
 * Provides statistics services for wagers.
 */
class StatisticsService {

  public function __construct(
    private readonly EntityTypeManagerInterface $entityTypeManager,
    private readonly GameManager $gameManager,
  ) {}

  /**
   * Checks if a user is a manager.
   */
  public function isManager(string $wxidRoom, string $wxid): bool {
    return $this->gameManager->isManager($wxidRoom, $wxid);
  }

  /**
   * Generates statistics for a room.
   */
  public function generateStatistics(string $wxidRoom): array {
    $storage = $this->entityTypeManager->getStorage('wager');
    $wagers = $storage->loadByProperties([
      'wxid_room' => $wxidRoom,
      'status_round' => RoundStatus::IN_PROGRESS->value,
    ]);

    $statistics = [
      'total_wagers' => count($wagers),
      'regions' => [],
      'numbers' => [],
      'animals' => [],
      'total_amount' => 0,
    ];

    foreach ($wagers as $wager) {
      $wagerJson = $wager->get('wager')->value ?? '';
      if (empty($wagerJson)) {
        continue;
      }

      $wagerArray = json_decode($wagerJson, TRUE);
      if (!$wagerArray) {
        continue;
      }

      $wagerData = WagerData::fromArray($wagerArray);
      $this->processWagerStatistics($wagerData, $statistics);
    }

    return $statistics;
  }

  /**
   * Formats statistics into a readable response.
   */
  public function formatStatisticsResponse(array $statistics): string {
    if ($statistics['total_wagers'] === 0) {
      return "📊 当前统计\r\r暂无下注记录";
    }

    $response = "📊 当前统计\r\r";
    $response .= "📝 总下注数: {$statistics['total_wagers']}\r";
    $response .= "💰 总金额: {$statistics['total_amount']}元\r\r";

    // Region statistics
    foreach ($statistics['regions'] as $region => $data) {
      $regionEnum = WagerRegion::from($region);
      $response .= "{$regionEnum->getEmoji()} {$regionEnum->getLabel()}:\r";
      $response .= "  下注数: {$data['count']}\r";
      $response .= "  金额: {$data['amount']}元\r\r";
    }

    // Number statistics
    if (!empty($statistics['numbers'])) {
      $response .= "🔢 号码统计:\r";
      ksort($statistics['numbers']);
      foreach ($statistics['numbers'] as $number => $amount) {
        $response .= "  {$number}号: {$amount}元\r";
      }
      $response .= "\r";
    }

    // Animal statistics
    if (!empty($statistics['animals'])) {
      $response .= "🐾 生肖统计:\r";
      foreach ($statistics['animals'] as $animal => $data) {
        $response .= "  {$animal}: {$data['display_amount']}元 (实际{$data['actual_amount']}元)\r";
      }
    }

    return $response;
  }

  /**
   * Processes wager data for statistics.
   */
  private function processWagerStatistics(WagerData $wagerData, array &$statistics): void {
    $region = $wagerData->region->value;
    
    // Initialize region if not exists
    if (!isset($statistics['regions'][$region])) {
      $statistics['regions'][$region] = ['count' => 0, 'amount' => 0];
    }

    $statistics['regions'][$region]['count']++;
    $wagerTotal = $wagerData->getTotalAmount();
    $statistics['regions'][$region]['amount'] += $wagerTotal;
    $statistics['total_amount'] += $wagerTotal;

    // Process number bets
    foreach ($wagerData->getNumberBets() as $bet) {
      if (!isset($statistics['numbers'][$bet->value])) {
        $statistics['numbers'][$bet->value] = 0;
      }
      $statistics['numbers'][$bet->value] += $bet->amount;
    }

    // Process animal bets
    foreach ($wagerData->getAnimalBets() as $bet) {
      if (!isset($statistics['animals'][$bet->value])) {
        $statistics['animals'][$bet->value] = [
          'display_amount' => 0,
          'actual_amount' => 0,
        ];
      }
      
      $animalNumbers = $this->getAnimalNumbers($bet->value);
      $actualAmount = $bet->amount * count($animalNumbers);
      
      $statistics['animals'][$bet->value]['display_amount'] += $bet->amount;
      $statistics['animals'][$bet->value]['actual_amount'] += $actualAmount;

      // Also add to numbers statistics
      foreach ($animalNumbers as $number) {
        if (!isset($statistics['numbers'][$number])) {
          $statistics['numbers'][$number] = 0;
        }
        $statistics['numbers'][$number] += $bet->amount;
      }
    }
  }

  /**
   * Gets numbers for a given animal (2025 Snake Year mapping).
   */
  private function getAnimalNumbers(string $animal): array {
    $animalNumbers = [
      '蛇' => ['01', '13', '25', '37', '49'], // Snake year - 5 numbers
      '鼠' => ['06', '18', '30', '42'],
      '牛' => ['05', '17', '29', '41'],
      '虎' => ['04', '16', '28', '40'],
      '兔' => ['03', '15', '27', '39'],
      '龙' => ['02', '14', '26', '38'],
      '马' => ['12', '24', '36', '48'],
      '羊' => ['11', '23', '35', '47'],
      '猴' => ['10', '22', '34', '46'],
      '鸡' => ['09', '21', '33', '45'],
      '狗' => ['08', '20', '32', '44'],
      '猪' => ['07', '19', '31', '43'],
    ];

    return $animalNumbers[$animal] ?? [];
  }
}
