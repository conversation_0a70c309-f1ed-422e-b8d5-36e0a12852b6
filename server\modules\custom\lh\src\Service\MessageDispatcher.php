<?php

declare(strict_types=1);

namespace Drupal\lh\Service;

use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use <PERSON>upal\lh\Handler\AdminCommandHandler;
use Drupal\lh\Handler\GameControlHandler;
use Drupal\lh\Handler\StatisticsHandler;
use Drupal\lh\Handler\WagerHandler;

/**
 * Dispatches WeChat messages to appropriate handlers.
 */
class MessageDispatcher {

  public function __construct(
    private readonly AdminCommandHandler $adminCommandHandler,
    private readonly GameControlHandler $gameControlHandler,
    private readonly StatisticsHandler $statisticsHandler,
    private readonly WagerHandler $wagerHandler,
    private readonly LoggerChannelInterface $logger,
  ) {}

  /**
   * Dispatches a message to the appropriate handler.
   */
  public function dispatch(array $data): array {
    $messageData = $data['data'] ?? [];
    $message = $messageData['msg'] ?? '';

    // Skip auto-reply messages to prevent loops
    if (str_starts_with($message, '哎哟，')) {
      return ['status' => 'ignored', 'reason' => 'Auto-reply message ignored'];
    }

    // Process messages by priority
    $handlers = [
      'admin_command' => $this->adminCommandHandler,
      'game_control' => $this->gameControlHandler,
      'statistics' => $this->statisticsHandler,
      'wager' => $this->wagerHandler,
    ];

    foreach ($handlers as $handlerName => $handler) {
      if ($handler->canHandle($data)) {
        $this->logger->info('Message handled by @handler', ['@handler' => $handlerName]);
        return $handler->handle($data);
      }
    }

    return ['status' => 'ignored', 'message' => 'No handler processed the message'];
  }

  /**
   * Handles WeChat framework injection success event.
   */
  public function handleInjectSuccess(array $data): array {
    $port = $data['port'] ?? 0;
    
    return [
      'status' => 'success',
      'message' => 'Injection success handled',
      'data' => [
        'port' => $port,
        'pid' => $data['data']['pid'] ?? 0,
        'flag' => $data['flag'] ?? '',
      ],
    ];
  }

  /**
   * Handles WeChat login success event.
   */
  public function handleLoginSuccess(array $data): array {
    return [
      'status' => 'success',
      'message' => 'Login success handled',
      'data' => [
        'wxid' => $data['wxid'] ?? '',
        'nick' => $data['data']['nick'] ?? '',
        'phone' => $data['data']['phone'] ?? '',
      ],
    ];
  }

  /**
   * Handles received message events.
   */
  public function handleRecvMsg(array $data): array {
    return $this->dispatch($data);
  }

  /**
   * Handles transfer payment events.
   */
  public function handleTransPay(array $data): array {
    return ['status' => 'success', 'message' => 'Transfer event handled'];
  }

  /**
   * Handles message revoke events.
   */
  public function handleRevokeMsg(array $data): array {
    return ['status' => 'success', 'message' => 'Revoke event handled'];
  }

  /**
   * Handles friend request events.
   */
  public function handleFriendReq(array $data): array {
    return ['status' => 'success', 'message' => 'Friend request handled'];
  }

  /**
   * Handles authorization expiration events.
   */
  public function handleAuthExpire(array $data): array {
    return ['status' => 'success', 'message' => 'Auth expire handled'];
  }

  /**
   * Handles QR code payment events.
   */
  public function handleQrPay(array $data): array {
    return ['status' => 'success', 'message' => 'QR pay event handled'];
  }

  /**
   * Handles group member changes events.
   */
  public function handleGroupMemberChanges(array $data): array {
    return ['status' => 'success', 'message' => 'Group member changes handled'];
  }
}
