<?php

declare(strict_types=1);

namespace Drupal\lh\ValueObject;

/**
 * Represents game statistics data.
 */
readonly class GameStatistics {

  public function __construct(
    public int $totalWagers,
    public int $totalAmount,
    public array $regions,
    public array $numbers,
    public array $animals,
  ) {}

  /**
   * Checks if there are any wagers.
   */
  public function hasWagers(): bool {
    return $this->totalWagers > 0;
  }

  /**
   * Gets the total number of unique numbers bet.
   */
  public function getUniqueNumberCount(): int {
    return count($this->numbers);
  }

  /**
   * Gets the total number of unique animals bet.
   */
  public function getUniqueAnimalCount(): int {
    return count($this->animals);
  }

  /**
   * Gets statistics for a specific region.
   */
  public function getRegionStatistics(string $region): ?array {
    return $this->regions[$region] ?? NULL;
  }

  /**
   * Gets amount for a specific number.
   */
  public function getNumberAmount(string $number): int {
    return $this->numbers[$number] ?? 0;
  }

  /**
   * Gets statistics for a specific animal.
   */
  public function getAnimalStatistics(string $animal): ?array {
    return $this->animals[$animal] ?? NULL;
  }

  /**
   * Converts to array format.
   */
  public function toArray(): array {
    return [
      'total_wagers' => $this->totalWagers,
      'total_amount' => $this->totalAmount,
      'regions' => $this->regions,
      'numbers' => $this->numbers,
      'animals' => $this->animals,
    ];
  }

  /**
   * Creates from array data.
   */
  public static function fromArray(array $data): self {
    return new self(
      $data['total_wagers'] ?? 0,
      $data['total_amount'] ?? 0,
      $data['regions'] ?? [],
      $data['numbers'] ?? [],
      $data['animals'] ?? []
    );
  }
}
